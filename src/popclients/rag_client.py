# -*- coding: utf-8 -*-
"""
RagClient - 封装阿里云无影AI内部RAG服务客户端
提供简化的接口来访问无影AI内部RAG服务
"""
from typing import Optional, Dict, Any, List
import sys
import os

try:
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_wuyingaiinner20250709 import client
    from alibabacloud_wuyingaiinner20250709 import models as rag_models
except ImportError:
    # 如果无法导入，尝试从本地路径导入
    try:
        from alibabacloud_tea_openapi import models as open_api_models
    except ImportError:
        # 如果还是无法导入，创建一个简单的模拟
        class MockConfig:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

        class MockModels:
            Config = MockConfig

        open_api_models = MockModels()

    # 导入本地的wuyingaiinner20250709
    from alibabacloud_wuyingaiinner20250709 import client
    from alibabacloud_wuyingaiinner20250709 import models as rag_models

from aliyunaklesscredprovider.core import AklessCredproviderFactory
from aliyunaklesscredprovider.core.config import CredentialProviderConfig
from loguru import logger


class RagClient:
    """
    无影AI内部RAG服务客户端封装类
    提供简化的接口来访问无影AI内部RAG服务
    """

    def __init__(
        self,
        endpoint: str = "wuyingaiinner-pre.aliyuncs.com",
        connect_timeout: int = 10000,
        read_timeout: int = 10000,
        **kwargs,
    ):
        """
        初始化RagClient

        Args:
            endpoint: 服务端点，默认为预发环境
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 从配置中读取RAM角色ARN
        try:
            # 尝试多种导入方式
            try:
                from src.shared.config.environments import env_manager
            except ImportError:
                import sys
                import os

                # 添加项目根目录到Python路径
                project_root = os.path.abspath(
                    os.path.join(os.path.dirname(__file__), "..", "..")
                )
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from src.shared.config.environments import env_manager

            config = env_manager.get_config()
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id

            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法使用无AK认证")

            logger.info(f"从配置中读取RAM角色ARN: {ram_role_arn} - 环境: {env_manager.current_env.value}")
        except Exception as e:
            logger.error(f"无法从配置中读取RAM角色ARN: {e}")
            raise ValueError(f"无法从配置中读取RAM角色ARN: {e}")

        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        self.ram_role_arn = ram_role_arn
        self.region_id = region_id

        # 使用无AK认证创建凭证
        try:
            config = CredentialProviderConfig(
                ram_role_arn=ram_role_arn,
                region_id=region_id,
                app_name="wuying-alpha-service",
                app_group="waiy-rag-service",
                app_env=config.akless_env or "testing"
            )
            cred_client = AklessCredproviderFactory.get_opensdk_v2_credential_with_config(config)
            logger.info("成功创建无AK凭证")
        except Exception as e:
            logger.error(f"创建无AK凭证失败: {e}")
            raise ValueError(f"创建无AK凭证失败: {e}")

        # 创建配置
        self.config = open_api_models.Config(
            credential=cred_client,
            region_id=region_id,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs,
        )
        # 确保端点被正确设置
        self.config.endpoint = endpoint

        # 初始化客户端
        self._client = client.Client(self.config)

        # 验证端点设置
        logger.info(f"RAG客户端端点设置: {self.config.endpoint}")
        logger.info(
            f"RAG客户端实际端点: {getattr(self._client, '_endpoint', 'Unknown')}"
        )

    # ==================== 知识库管理相关方法 ====================

    # 知识库管理相关方法
    def create_kb(
        self,
        name: str,
        description: Optional[str] = None,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.CreateKBRagResponse:
        """
        创建知识库

        Args:
            name: 知识库名称
            description: 知识库描述
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            CreateKBRagResponse: 创建知识库响应
        """
        try:
            logger.info(f"创建知识库: {name}")
            request = rag_models.CreateKBRagRequest(
                name=name, description=description, ali_uid=ali_uid, wy_id=wy_id
            )
            response = self._client.create_kbrag(request)
            logger.info(f"创建知识库返回: {response.body}")
            return response
        except Exception as e:
            raise RagClientError(f"创建知识库失败: {str(e)}") from e

    async def create_kb_async(
        self,
        name: str,
        description: Optional[str] = None,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.CreateKBRagResponse:
        """
        异步创建知识库

        Args:
            name: 知识库名称
            description: 知识库描述
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            CreateKBRagResponse: 创建知识库响应
        """
        try:
            request = rag_models.CreateKBRagRequest(
                name=name, description=description, ali_uid=ali_uid, wy_id=wy_id
            )
            return await self._client.create_kbrag_async(request)
        except Exception as e:
            raise RagClientError(f"异步创建知识库失败: {str(e)}") from e

    def delete_kb(self, kb_id: str) -> rag_models.DeleteKBRagResponse:
        """
        删除知识库

        Args:
            kb_id: 知识库ID

        Returns:
            DeleteKBRagResponse: 删除知识库响应
        """
        try:
            request = rag_models.DeleteKBRagRequest(kb_id=kb_id)
            return self._client.delete_kbrag(request)
        except Exception as e:
            raise RagClientError(f"删除知识库失败: {str(e)}") from e

    async def delete_kb_async(self, kb_id: str) -> rag_models.DeleteKBRagResponse:
        """
        异步删除知识库

        Args:
            kb_id: 知识库ID

        Returns:
            DeleteKBRagResponse: 删除知识库响应
        """
        try:
            request = rag_models.DeleteKBRagRequest(kb_id=kb_id)
            return await self._client.delete_kbrag_async(request)
        except Exception as e:
            raise RagClientError(f"异步删除知识库失败: {str(e)}") from e

    def update_kb(
        self, kb_id: str, name: Optional[str] = None, description: Optional[str] = None
    ) -> rag_models.UpdateKBRagResponse:
        """
        更新知识库

        Args:
            kb_id: 知识库ID
            name: 新的知识库名称
            description: 新的知识库描述

        Returns:
            UpdateKBRagResponse: 更新知识库响应
        """
        try:
            request = rag_models.UpdateKBRagRequest(
                kb_id=kb_id, name=name, description=description
            )
            return self._client.update_kbrag(request)
        except Exception as e:
            raise RagClientError(f"更新知识库失败: {str(e)}") from e

    async def update_kb_async(
        self, kb_id: str, name: Optional[str] = None, description: Optional[str] = None
    ) -> rag_models.UpdateKBRagResponse:
        """
        异步更新知识库

        Args:
            kb_id: 知识库ID
            name: 新的知识库名称
            description: 新的知识库描述

        Returns:
            UpdateKBRagResponse: 更新知识库响应
        """
        try:
            request = rag_models.UpdateKBRagRequest(
                kb_id=kb_id, name=name, description=description
            )
            return await self._client.update_kbrag_async(request)
        except Exception as e:
            raise RagClientError(f"异步更新知识库失败: {str(e)}") from e

    def list_user_kbs(
        self,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> rag_models.ListUserKBsRagResponse:
        """
        获取用户知识库列表

        Args:
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            ListUserKBsRagResponse: 用户知识库列表响应
        """
        try:
            request = rag_models.ListUserKBsRagRequest(
                ali_uid=ali_uid, wy_id=wy_id, limit=limit, offset=offset
            )
            return self._client.list_user_kbs_rag(request)
        except Exception as e:
            raise RagClientError(f"获取用户知识库列表失败: {str(e)}") from e

    async def list_user_kbs_async(
        self,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> rag_models.ListUserKBsRagResponse:
        """
        异步获取用户知识库列表

        Args:
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            ListUserKBsRagResponse: 用户知识库列表响应
        """
        try:
            request = rag_models.ListUserKBsRagRequest(
                ali_uid=ali_uid, wy_id=wy_id, limit=limit, offset=offset
            )
            return await self._client.list_user_kbs_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步获取用户知识库列表失败: {str(e)}") from e

    # ==================== 文档管理相关方法 ====================

    # 文档管理相关方法
    def parse_document_by_url(
        self,
        url: str,
        filename: str,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.ParseDocumentByUrlRagResponse:
        """
        通过URL解析文档

        Args:
            url: 文档URL
            filename: 文件名
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            ParseDocumentByUrlRagResponse: 解析文档响应
        """
        try:
            request = rag_models.ParseDocumentByUrlRagRequest(
                url=url, filename=filename, ali_uid=ali_uid, wy_id=wy_id
            )
            return self._client.parse_document_by_url_rag(request)
        except Exception as e:
            raise RagClientError(f"解析文档失败: {str(e)}") from e

    async def parse_document_by_url_async(
        self,
        url: str,
        filename: str,
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.ParseDocumentByUrlRagResponse:
        """
        异步通过URL解析文档

        Args:
            url: 文档URL
            filename: 文件名
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            ParseDocumentByUrlRagResponse: 解析文档响应
        """
        try:
            request = rag_models.ParseDocumentByUrlRagRequest(
                url=url, filename=filename, ali_uid=ali_uid, wy_id=wy_id
            )
            return await self._client.parse_document_by_url_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步解析文档失败: {str(e)}") from e

    def submit_document_by_url(
        self,
        url: str,
        filename: str,
        kb_ids: List[str],
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.SubmitDocumentByUrlRagResponse:
        """
        通过URL提交文档到知识库

        Args:
            url: 文档URL
            filename: 文件名
            kb_ids: 知识库ID列表
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            SubmitDocumentByUrlRagResponse: 提交文档响应
        """
        try:
            request = rag_models.SubmitDocumentByUrlRagRequest(
                url=url, filename=filename, kb_ids=kb_ids, ali_uid=ali_uid, wy_id=wy_id
            )
            return self._client.submit_document_by_url_rag(request)
        except Exception as e:
            raise RagClientError(f"提交文档失败: {str(e)}") from e

    async def submit_document_by_url_async(
        self,
        url: str,
        filename: str,
        kb_ids: List[str],
        ali_uid: Optional[str] = None,
        wy_id: Optional[str] = None,
    ) -> rag_models.SubmitDocumentByUrlRagResponse:
        """
        异步通过URL提交文档到知识库

        Args:
            url: 文档URL
            filename: 文件名
            kb_ids: 知识库ID列表
            ali_uid: 阿里云用户ID
            wy_id: 无影用户ID

        Returns:
            SubmitDocumentByUrlRagResponse: 提交文档响应
        """
        try:
            request = rag_models.SubmitDocumentByUrlRagRequest(
                url=url, filename=filename, kb_ids=kb_ids, ali_uid=ali_uid, wy_id=wy_id
            )
            return await self._client.submit_document_by_url_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步提交文档失败: {str(e)}") from e

    def delete_document(self, doc_id: str) -> rag_models.DeleteDocumentRagResponse:
        """
        删除文档

        Args:
            doc_id: 文档ID

        Returns:
            DeleteDocumentRagResponse: 删除文档响应
        """
        try:
            request = rag_models.DeleteDocumentRagRequest(doc_id=doc_id)
            return self._client.delete_document_rag(request)
        except Exception as e:
            raise RagClientError(f"删除文档失败: {str(e)}") from e

    async def delete_document_async(
        self, doc_id: str
    ) -> rag_models.DeleteDocumentRagResponse:
        """
        异步删除文档

        Args:
            doc_id: 文档ID

        Returns:
            DeleteDocumentRagResponse: 删除文档响应
        """
        try:
            request = rag_models.DeleteDocumentRagRequest(doc_id=doc_id)
            return await self._client.delete_document_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步删除文档失败: {str(e)}") from e

    def modify_documents_kb(
        self, kb_id: str, doc_ids: List[str], operation: str
    ) -> rag_models.ModifyDocumentsKBRagResponse:
        """
        修改知识库中的文档

        Args:
            kb_id: 知识库ID
            doc_ids: 文档ID列表
            operation: 操作类型

        Returns:
            ModifyDocumentsKBRagResponse: 修改文档响应
        """
        try:
            request = rag_models.ModifyDocumentsKBRagRequest(
                kb_id=kb_id, doc_ids=doc_ids, operation=operation
            )
            return self._client.modify_documents_kbrag(request)
        except Exception as e:
            raise RagClientError(f"修改知识库文档失败: {str(e)}") from e

    async def modify_documents_kb_async(
        self, kb_id: str, doc_ids: List[str], operation: str
    ) -> rag_models.ModifyDocumentsKBRagResponse:
        """
        异步修改知识库中的文档

        Args:
            kb_id: 知识库ID
            doc_ids: 文档ID列表
            operation: 操作类型

        Returns:
            ModifyDocumentsKBRagResponse: 修改文档响应
        """
        try:
            request = rag_models.ModifyDocumentsKBRagRequest(
                kb_id=kb_id, doc_ids=doc_ids, operation=operation
            )
            return await self._client.modify_documents_kbrag_async(request)
        except Exception as e:
            raise RagClientError(f"异步修改知识库文档失败: {str(e)}") from e

    # 文档状态检查相关方法
    def check_doc_status(self, doc_id: str) -> rag_models.CheckDocStatusRagResponse:
        """
        检查单个文档状态

        Args:
            doc_id: 文档ID

        Returns:
            CheckDocStatusRagResponse: 文档状态响应
        """
        try:
            request = rag_models.CheckDocStatusRagRequest(doc_id=doc_id)
            return self._client.check_doc_status_rag(request)
        except Exception as e:
            raise RagClientError(f"检查文档状态失败: {str(e)}") from e

    async def check_doc_status_async(
        self, doc_id: str
    ) -> rag_models.CheckDocStatusRagResponse:
        """
        异步检查单个文档状态

        Args:
            doc_id: 文档ID

        Returns:
            CheckDocStatusRagResponse: 文档状态响应
        """
        try:
            request = rag_models.CheckDocStatusRagRequest(doc_id=doc_id)
            return await self._client.check_doc_status_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步检查文档状态失败: {str(e)}") from e

    def check_docs_status(
        self, doc_ids: List[str]
    ) -> rag_models.CheckDocsStatusRagResponse:
        """
        检查多个文档状态

        Args:
            doc_ids: 文档ID列表

        Returns:
            CheckDocsStatusRagResponse: 文档状态响应
        """
        try:
            request = rag_models.CheckDocsStatusRagRequest(doc_ids=doc_ids)
            return self._client.check_docs_status_rag(request)
        except Exception as e:
            raise RagClientError(f"检查文档状态失败: {str(e)}") from e

    async def check_docs_status_async(
        self, doc_ids: List[str]
    ) -> rag_models.CheckDocsStatusRagResponse:
        """
        异步检查多个文档状态

        Args:
            doc_ids: 文档ID列表

        Returns:
            CheckDocsStatusRagResponse: 文档状态响应
        """
        try:
            request = rag_models.CheckDocsStatusRagRequest(doc_ids=doc_ids)
            return await self._client.check_docs_status_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步检查文档状态失败: {str(e)}") from e

    # ==================== RAG生成相关方法 ====================

    def generate_rag(
        self,
        q: str,
        kb_ids: List[str],
        query_paraments: Optional[rag_models.GenerateRagRequestQueryParaments] = None,
    ) -> rag_models.GenerateRagResponse:
        """
        RAG生成

        Args:
            q: 查询问题
            kb_ids: 知识库ID列表
            query_paraments: 查询参数

        Returns:
            GenerateRagResponse: RAG生成响应
        """
        try:
            request = rag_models.GenerateRagRequest(
                q=q, kb_ids=kb_ids, query_paraments=query_paraments
            )
            return self._client.generate_rag(request)
        except Exception as e:
            raise RagClientError(f"RAG生成失败: {str(e)}") from e

    async def generate_rag_async(
        self,
        q: str,
        kb_ids: List[str],
        query_paraments: Optional[rag_models.GenerateRagRequestQueryParaments] = None,
    ) -> rag_models.GenerateRagResponse:
        """
        异步RAG生成

        Args:
            q: 查询问题
            kb_ids: 知识库ID列表
            query_paraments: 查询参数

        Returns:
            GenerateRagResponse: RAG生成响应
        """
        try:
            request = rag_models.GenerateRagRequest(
                q=q, kb_ids=kb_ids, query_paraments=query_paraments
            )
            return await self._client.generate_rag_async(request)
        except Exception as e:
            raise RagClientError(f"异步RAG生成失败: {str(e)}") from e

    async def rag_generate_async(
        self,
        q: str,
        kb_ids: List[str],
        uid: Optional[str] = None,
        query_paraments: Optional[rag_models.RagGenerateRequestQueryParaments] = None,
    ) -> rag_models.RagGenerateResponse:
        """
        异步RAG生成（另一个接口）

        Args:
            q: 查询问题
            kb_ids: 知识库ID列表
            uid: 用户ID
            query_paraments: 查询参数

        Returns:
            RagGenerateResponse: RAG生成响应
        """
        try:
            request = rag_models.RagGenerateRequest(
                q=q, kb_ids=kb_ids, uid=uid, query_paraments=query_paraments
            )
            return await self._client.rag_generate_async(request)
        except Exception as e:
            raise RagClientError(f"异步RAG生成失败: {str(e)}") from e

    # ==================== 知识库会话相关方法 ====================

    def submit_snippet(
        self,
        kb_ids: List[str],
        messages: List[rag_models.SubmitSnippetRagRequestMessages],
        ali_uid: str = None,
        wy_id: str = None,
    ) -> rag_models.SubmitSnippetRagResponse:
        """
        提交片段
        """
        try:
            request = rag_models.SubmitSnippetRagRequest(
                messages=messages,
                kb_ids=kb_ids,
                ali_uid=ali_uid,
                wy_id=wy_id,
            )
            return self._client.submit_snippet_rag(request)
        except Exception as e:
            raise RagClientError(f"提交片段失败: {str(e)}") from e

    def check_snippet_status(
        self,
        snippet_ids: List[str],
    ) -> rag_models.CheckSnippetsStatusResponse:
        """
        提交片段
        """
        try:
            request = rag_models.CheckSnippetsStatusRequest(
                snippet_ids=snippet_ids,
            )
            return self._client.check_snippets_status(request)
        except Exception as e:
            raise RagClientError(f"检查片段状态失败: {str(e)}") from e

    def delete_snippet(
        self,
        snippet_id: str,
    ) -> rag_models.DeleteSnippetRagResponse:
        """
        删除片段
        """
        try:
            request = rag_models.DeleteSnippetRagRequest(
                snippet_id=snippet_id,
            )
            return self._client.delete_snippet_rag(request)
        except Exception as e:
            raise RagClientError(f"删除片段失败: {str(e)}") from e

    # ==================== 华丽的分割线 ====================

    # 实用方法
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息

        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "ram_role_arn": self.ram_role_arn,
            "region_id": self.region_id,
            "endpoint": self.endpoint or "wuyingaiinner-pre.aliyuncs.com",
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout,
        }

    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return (
            f"RagClient(endpoint={self.endpoint}, ram_role_arn={self.ram_role_arn})"
        )

    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class RagClientError(Exception):
    """RagClient异常类"""

    pass


# 全局单例实例
_rag_client_instance = None


# 便捷函数
def create_rag_client(
    endpoint: Optional[str] = None,
    **kwargs,
) -> RagClient:
    """
    创建RagClient实例的便捷函数（单例模式）

    Args:
        endpoint: 服务端点，如果为None则使用默认值
        **kwargs: 其他配置参数

    Returns:
        RagClient: 客户端实例（单例）
    """
    global _rag_client_instance

    # 如果实例已存在，直接返回
    if _rag_client_instance is not None:
        return _rag_client_instance

    # 如果没有指定端点，使用默认的预发环境端点
    if endpoint is None:
        endpoint = "wuyingaiinner-pre.aliyuncs.com"

    # 创建新实例
    _rag_client_instance = RagClient(
        endpoint=endpoint,
        **kwargs,
    )

    return _rag_client_instance

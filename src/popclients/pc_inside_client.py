# -*- coding: utf-8 -*-
"""
PcInsideClient - 封装阿里云无影PC内部服务客户端
提供简化的接口来访问无影PC内部服务
"""
from typing import Optional, Dict, Any
import sys
import os

# 添加本地wuying-pc-inside-20221123包到Python路径
current_dir = os.path.dirname(__file__)
wuying_pc_path = os.path.join(current_dir, 'wuying-pc-inside-20221123')
if wuying_pc_path not in sys.path:
    sys.path.insert(0, wuying_pc_path)

try:
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_wuying_pc_inside20221123 import client
    from alibabacloud_wuying_pc_inside20221123 import models as pc_models
except ImportError:
    # 如果无法导入，尝试从本地路径导入
    try:
        from alibabacloud_tea_openapi import models as open_api_models
    except ImportError:
        # 如果还是无法导入，创建一个简单的模拟
        class MockConfig:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

        class MockModels:
            Config = MockConfig

        open_api_models = MockModels()

    # 导入本地的wuying_pc_inside20221123
    from alibabacloud_wuying_pc_inside20221123 import client
    from alibabacloud_wuying_pc_inside20221123 import models as pc_models

from aliyunaklesscredprovider.core import AklessCredproviderFactory
from loguru import logger


class PcInsideClient:
    """
    无影PC内部服务客户端封装类
    提供简化的接口来访问无影PC内部服务
    """
    
    def __init__(
        self,
        endpoint: Optional[str] = None,
        connect_timeout: int = 5000,
        read_timeout: int = 10000,
        **kwargs
    ):
        """
        初始化PC内部服务客户端

        Args:
            endpoint: 服务端点，默认为无影PC内部服务端点
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 从配置中读取RAM角色ARN
        try:
            # 尝试多种导入方式
            try:
                from src.shared.config.environments import env_manager
            except ImportError:
                import sys
                import os
                # 添加项目根目录到Python路径
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from src.shared.config.environments import env_manager

            config = env_manager.get_config()
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id

            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法使用无AK认证")

            logger.info(f"从配置中读取RAM角色ARN: {ram_role_arn} - 环境: {env_manager.current_env.value}")
        except Exception as e:
            logger.error(f"无法从配置中读取RAM角色ARN: {e}")
            raise PcInsideClientError(f"无法从配置中读取RAM角色ARN: {e}")

        # 设置默认端点
        if not endpoint:
            endpoint = "wuying-pc-inside-inner-share.cn-hangzhou.aliyuncs.com"

        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        self.ram_role_arn = ram_role_arn
        self.region_id = region_id

        # 使用无AK认证创建凭证
        try:
            cred_client = AklessCredproviderFactory.get_opensdk_v2_credential(ram_role_arn)
            logger.info("成功创建无AK凭证")
        except Exception as e:
            logger.error(f"创建无AK凭证失败: {e}")
            raise PcInsideClientError(f"创建无AK凭证失败: {e}")

        # 创建配置
        self.config = open_api_models.Config(
            credential=cred_client,
            region_id=region_id,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs
        )
        # 确保端点被正确设置
        self.config.endpoint = endpoint

        # 初始化客户端
        self._client = client.Client(self.config)

        # 验证端点设置
        logger.info(f"PC内部客户端端点设置: {self.config.endpoint}")
        logger.info(f"PC内部客户端实际端点: {getattr(self._client, '_endpoint', 'Unknown')}")
    
    # 桌面管理相关方法
    def describe_desktops(
        self,
        end_user_id: Optional[str] = None,
    ) -> pc_models.DescribeDesktopsResponse:
        """
        查询桌面信息

        Args:
            create_user_id: 创建用户ID
            end_user_id: 终端用户ID
            team_id: 团队ID

        Returns:
            DescribeDesktopsResponse: 查询桌面响应
        """
        try:
            request = pc_models.DescribeDesktopsRequest(
                display_type="AIPC",
                end_user_id=end_user_id,
            )
            return self._client.describe_desktops(request)
        except Exception as e:
            raise PcInsideClientError(f"查询桌面信息失败: {str(e)}") from e

    async def describe_desktops_async(
        self,
        create_user_id: Optional[str] = None,
        display_type: Optional[str] = None,
        end_user_id: Optional[str] = None,
        team_id: Optional[str] = None
    ) -> pc_models.DescribeDesktopsResponse:
        """
        异步查询桌面信息

        Args:
            create_user_id: 创建用户ID
            display_type: 显示类型
            end_user_id: 终端用户ID
            team_id: 团队ID

        Returns:
            DescribeDesktopsResponse: 查询桌面响应
        """
        try:
            request = pc_models.DescribeDesktopsRequest(
                create_user_id=create_user_id,
                display_type=display_type,
                end_user_id=end_user_id,
                team_id=team_id
            )
            return await self._client.describe_desktops_async(request)
        except Exception as e:
            raise PcInsideClientError(f"异步查询桌面信息失败: {str(e)}") from e

    # 实用方法
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息

        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "ram_role_arn": self.ram_role_arn,
            "region_id": self.region_id,
            "endpoint": self.endpoint or "wuying-pc-inside-inner-share.cn-hangzhou.aliyuncs.com",
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout
        }

    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return f"PcInsideClient(endpoint={self.endpoint}, ram_role_arn={self.ram_role_arn})"

    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class PcInsideClientError(Exception):
    """PcInsideClient异常类"""
    pass


# 全局单例实例
_pc_inside_client_instance = None


def get_pc_inside_client(
    endpoint: Optional[str] = None,
    **kwargs
) -> PcInsideClient:
    """
    获取PC内部服务客户端单例实例

    Args:
        endpoint: 服务端点
        **kwargs: 其他配置参数

    Returns:
        PcInsideClient: PC内部服务客户端实例
    """
    global _pc_inside_client_instance

    if _pc_inside_client_instance is None:
        _pc_inside_client_instance = PcInsideClient(
            endpoint=endpoint,
            **kwargs
        )

    return _pc_inside_client_instance


def reset_pc_inside_client():
    """重置PC内部服务客户端单例实例"""
    global _pc_inside_client_instance
    _pc_inside_client_instance = None
